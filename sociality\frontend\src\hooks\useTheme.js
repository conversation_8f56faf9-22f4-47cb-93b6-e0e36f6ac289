/**
 * Theme management hook
 * Provides theme switching functionality with persistence and Chakra UI integration
 */
import { useEffect, useCallback } from 'react';
import { useRecoilState } from 'recoil';
import { useColorMode } from '@chakra-ui/react';
import { themeAtom } from '../atoms';
import { STORAGE_KEYS } from '../utils/constants';
import { applyThemeToDocument } from '../utils/themeUtils';

const useTheme = () => {
  const [theme, setTheme] = useRecoilState(themeAtom);
  const { colorMode, setColorMode } = useColorMode();

  // Initialize theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem(STORAGE_KEYS.THEME);
    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
      setTheme(savedTheme);
      setColorMode(savedTheme);
    } else {
      // Default to light mode if no saved preference
      setTheme('light');
      setColorMode('light');
      localStorage.setItem(STORAGE_KEYS.THEME, 'light');
    }
  }, [setTheme, setColorMode]);

  // Sync theme changes with Chakra UI, document, and localStorage
  useEffect(() => {
    if (theme !== colorMode) {
      setColorMode(theme);
    }
    applyThemeToDocument(theme);
    localStorage.setItem(STORAGE_KEYS.THEME, theme);
  }, [theme, colorMode, setColorMode]);

  // Toggle between light and dark themes
  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
  }, [theme, setTheme]);

  // Set specific theme
  const setSpecificTheme = useCallback((newTheme) => {
    if (newTheme === 'light' || newTheme === 'dark') {
      setTheme(newTheme);
    }
  }, [setTheme]);

  // Check if current theme is dark
  const isDark = theme === 'dark';

  // Check if current theme is light
  const isLight = theme === 'light';

  return {
    theme,
    isDark,
    isLight,
    toggleTheme,
    setTheme: setSpecificTheme,
  };
};

export default useTheme;
